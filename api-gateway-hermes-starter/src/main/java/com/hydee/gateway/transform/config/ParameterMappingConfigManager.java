package com.hydee.gateway.transform.config;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 参数映射配置管理器
 * 负责加载和管理参数转换配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
@Component
public class ParameterMappingConfigManager {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ResourceLoader resourceLoader;
    
    /**
     * 配置缓存，key为路径模式，value为配置
     */
    private final Map<String, ParameterMappingConfig> configCache = new ConcurrentHashMap<>();
    
    @Value("${gateway.parameter.transform.config.location:classpath:parameter-mapping-config.json}")
    private String configLocation;
    
    @Value("${gateway.parameter.transform.enabled:false}")
    private boolean transformEnabled;
    
    public ParameterMappingConfigManager(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }
    
    @PostConstruct
    public void init() {
        if (transformEnabled) {
            loadConfigurations();
        }
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfigurations() {
        try {
            Resource resource = resourceLoader.getResource(configLocation);
            if (resource.exists()) {
                // 这里简化处理，实际可以支持多种配置格式
                ParameterMappingConfig config = objectMapper.readValue(resource.getInputStream(), ParameterMappingConfig.class);
                // 默认配置，匹配所有路径
                configCache.put("/**", config);
                log.info("参数转换配置加载成功: {}", configLocation);
            } else {
                log.warn("参数转换配置文件不存在: {}", configLocation);
            }
        } catch (IOException e) {
            log.error("加载参数转换配置失败: {}", configLocation, e);
        }
    }

    /**
     * 根据请求路径获取配置
     *
     * @param path 请求路径
     * @return 参数映射配置，如果没有匹配的配置则返回null
     */
    public ParameterMappingConfig getConfig(String path) {
        if (!isTransformEnabled() || !StringUtils.hasText(path)) {
            return null;
        }

        String mapping = "{\n" +
                "  \"request\": {\n" +
                "    \"mapping\": {\n" +
                "      \"header\": {\n" +
                "        \"headerTest\": \"header.user-key\"\n" +
                "      },\n" +
                "      \"query\": {\n" +
                "        \"scene\": \"query.device\"\n" +
                "      },\n" +
                "      \"body\": {\n" +
                "        \"scene\": \"body.device\",\n" +
                "        \"bizCode\": \"const.固定编码示例\",\n" +
                "        \"userId\": \"sys.userId\",\n" +
                "        \"diseaseDictId\": \"${body.dId ? 1 : 0}\",\n" +
                "        \"requestId\": \"${defaultEmpty(body.requestId, '11111'}\"\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"response\": {\n" +
                "    \"mapping\": {\n" +
                "      \"subMsg\": \"subMessage\",\n" +
                "      \"message\": \"msg\",\n" +
                "      \"data.bizID\": \"data.id\",\n" +
                "      \"data.bizConst\": \"const.12090\",\n" +
                "      \"data.sysTest\": \"sys.sysParam\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        // 这里简化处理，实际可以支持多种配置格式
        ParameterMappingConfig config = JSON.parseObject(mapping, ParameterMappingConfig.class);
        // 默认配置，匹配所有路径

        if (config != null) {
            return config;
        }
        return null;
    }
    
    /**
     * 添加配置
     * 
     * @param pathPattern 路径模式
     * @param config 配置
     */
    public void addConfig(String pathPattern, ParameterMappingConfig config) {
        configCache.put(pathPattern, config);
        log.info("添加参数转换配置: {} -> {}", pathPattern, config);
    }
    
    /**
     * 移除配置
     * 
     * @param pathPattern 路径模式
     */
    public void removeConfig(String pathPattern) {
        configCache.remove(pathPattern);
        log.info("移除参数转换配置: {}", pathPattern);
    }
    
    /**
     * 清空所有配置
     */
    public void clearConfigs() {
        configCache.clear();
        log.info("清空所有参数转换配置");
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfigurations() {
        clearConfigs();
        loadConfigurations();
    }
    
    /**
     * 简单的路径匹配实现
     * 支持 * 和 ** 通配符
     */
    private boolean pathMatches(String pattern, String path) {
        if ("/**".equals(pattern)) {
            return true;
        }
        
        if (pattern.equals(path)) {
            return true;
        }
        
        // 简单的通配符匹配实现
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix);
        }
        
        if (pattern.endsWith("/*")) {
            String prefix = pattern.substring(0, pattern.length() - 2);
            return path.startsWith(prefix) && !path.substring(prefix.length()).contains("/");
        }
        
        return false;
    }
    
    /**
     * 检查转换功能是否启用
     */
    public boolean isTransformEnabled() {
        return true;
    }
    
    /**
     * 获取所有配置
     */
    public Map<String, ParameterMappingConfig> getAllConfigs() {
        return new ConcurrentHashMap<>(configCache);
    }
}
