package com.hydee.gateway.transform.filter;

import com.hydee.gateway.transform.config.ParameterMappingConfig;
import com.hydee.gateway.transform.config.ParameterMappingConfigManager;
import com.hydee.gateway.transform.core.CachedBodyParameterTransformer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * 缓存请求体的参数转换网关过滤器工厂
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Slf4j
//@Component
public class CachedBodyParameterTransformGatewayFilterFactory extends AbstractGatewayFilterFactory<CachedBodyParameterTransformGatewayFilterFactory.Config> {
    
    private final ParameterMappingConfigManager configManager;
    private final CachedBodyParameterTransformer parameterTransformer;
    
    public CachedBodyParameterTransformGatewayFilterFactory(ParameterMappingConfigManager configManager, 
                                                           CachedBodyParameterTransformer parameterTransformer) {
        super(Config.class);
        this.configManager = configManager;
        this.parameterTransformer = parameterTransformer;
    }
    
    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!configManager.isTransformEnabled()) {
                return chain.filter(exchange);
            }
            
            String path = exchange.getRequest().getPath().pathWithinApplication().value();
            ParameterMappingConfig mappingConfig = configManager.getConfig(path);
            
            if (mappingConfig == null) {
                return chain.filter(exchange);
            }
            
            log.debug("应用参数转换配置到路径: {}", path);
            
            // 首先缓存请求体
            return cacheRequestBody(exchange)
                .flatMap(cachedExchange -> {
                    // 转换请求参数
                    return parameterTransformer.transformRequest(cachedExchange, mappingConfig)
                        .flatMap(transformedExchange -> {
                            // 包装响应以转换响应参数
                            ServerHttpResponse originalResponse = transformedExchange.getResponse();
                            DataBufferFactory bufferFactory = originalResponse.bufferFactory();
                            
                            ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
                                @Override
                                public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                                    if (body instanceof Flux) {
                                        Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                                        return super.writeWith(fluxBody.collectList().flatMapMany(dataBuffers -> {
                                            // 读取响应体
                                            StringBuilder responseBody = new StringBuilder();
                                            dataBuffers.forEach(dataBuffer -> {
                                                byte[] content = new byte[dataBuffer.readableByteCount()];
                                                dataBuffer.read(content);
                                                DataBufferUtils.release(dataBuffer);
                                                responseBody.append(new String(content, StandardCharsets.UTF_8));
                                            });
                                            
                                            // 转换响应参数
                                            String originalResponseStr = responseBody.toString();
                                            return parameterTransformer.transformResponse(originalResponseStr, mappingConfig, transformedExchange)
                                                .map(transformedResponseStr -> {
                                                    return bufferFactory.wrap(transformedResponseStr.getBytes(StandardCharsets.UTF_8));
                                                })
                                                .onErrorReturn(bufferFactory.wrap(originalResponseStr.getBytes(StandardCharsets.UTF_8)))
                                                .flux();
                                        }));
                                    }
                                    return super.writeWith(body);
                                }
                            };
                            
                            return chain.filter(transformedExchange.mutate().response(decoratedResponse).build());
                        });
                })
                .onErrorResume(throwable -> {
                    log.error("参数转换过程中发生错误", throwable);
                    return chain.filter(exchange);
                });
        };
    }
    
    /**
     * 缓存请求体
     */
    private Mono<ServerWebExchange> cacheRequestBody(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 如果是GET请求或者没有请求体，直接返回
        if ("GET".equalsIgnoreCase(request.getMethodValue()) || 
            request.getHeaders().getContentLength() == 0) {
            exchange.getAttributes().put("cachedRequestBody", "");
            return Mono.just(exchange);
        }
        
        return request.getBody()
            .map(dataBuffer -> {
                byte[] bytes = new byte[dataBuffer.readableByteCount()];
                dataBuffer.read(bytes);
                DataBufferUtils.release(dataBuffer);
                return new String(bytes, StandardCharsets.UTF_8);
            })
            .reduce("", (s1, s2) -> s1 + s2)
            .defaultIfEmpty("")
            .map(requestBody -> {
                // 缓存请求体到exchange属性中
                exchange.getAttributes().put("cachedRequestBody", requestBody);
                
                // 创建新的请求，使用缓存的请求体
                DataBufferFactory bufferFactory = exchange.getResponse().bufferFactory();
                DataBuffer buffer = bufferFactory.wrap(requestBody.getBytes(StandardCharsets.UTF_8));
                
                ServerHttpRequest newRequest = new ServerHttpRequestDecorator(request) {
                    @Override
                    public Flux<DataBuffer> getBody() {
                        return Flux.just(buffer);
                    }
                };
                
                return exchange.mutate().request(newRequest).build();
            });
    }
    
    @Data
    public static class Config {
        private boolean enabled = true;
        private String configPath;
    }
}
